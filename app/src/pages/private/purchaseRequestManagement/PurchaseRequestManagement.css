.purchase-request-management {
  /* Main container styling */
}

.tab-content {
  padding-top: 1rem;
}

/* Status badge styling */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-pending {
  background-color: #fef3c7;
  color: #92400e;
}

.status-approved {
  background-color: #d1fae5;
  color: #065f46;
}

.status-rejected {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-cancelled {
  background-color: #f3f4f6;
  color: #374151;
}

/* Products modal styling */
.products-modal-content {
  /* Modal content styling */
}

.products-list {
  max-height: 400px;
  overflow-y: auto;
}

.product-item {
  background-color: #f9fafb;
  border-color: #e5e7eb !important;
}

.product-info {
  flex: 1;
}

.quantity-info {
  flex-shrink: 0;
  margin-left: 1rem;
}

/* User and category name cells */
.user-name-cell,
.category-name-cell {
  /* Cell styling if needed */
}
